import PropTypes from 'prop-types';
import { Box, IconButton } from '@mui/material';
import ButtonWrap from '../assets/scripts/button_wrap';

DeviceController.propTypes = {
  onTopLeft: PropTypes.func,
  onTopRight: PropTypes.func,
  onBottomLeft: PropTypes.func,
  onBottomRight: PropTypes.func,
  onCenter:PropTypes.func,
  topLeftContent: PropTypes.node,
  topRightContent: PropTypes.node,
  bottomLeftContent: PropTypes.node,
  bottomRightContent: PropTypes.node,
  centerContent:PropTypes.node,
}

const buttonStyle = {
  position: 'absolute',
  transform: 'translate(-50%,-50%)',
  maxWidth: '31%',
  maxHeight: '31%',
  minWidth: '31%',
  minHeight: '31%',
  color: 'grey.100'

}

export default function DeviceController({
  onTopLeft,
  onTopRight,
  onBottomLeft,
  onBottomRight,
  onCenter,
  topLeftContent,
  topRightContent,
  bottomLeftContent,
  bottomRightContent,
  centerContent,
  ...other }) {

  return (
    <Box
      position={'relative'}
      width={240}
      height={240}
      m={'auto'}
      sx={{
        overflow: 'visible', // Allow content to overflow for schedule indicator
        paddingTop: '30px', // Add space for the indicator
        marginTop: '-30px' // Compensate for the padding
      }}
      {...other}
    >
      
      <ButtonWrap color={'grey.50032'} />
      
      <IconButton sx={{ top: '21.5%', left: '20.8%', ...buttonStyle }} onClick={onTopLeft} >
        {topLeftContent}
      </IconButton>
      <IconButton sx={{ top: '21.5%', left: '78.8%', ...buttonStyle }} onClick={onTopRight}>
        {topRightContent}
      </IconButton>
      <IconButton sx={{ top: '79.5%', left: '20.8%', ...buttonStyle }} onClick={onBottomLeft}>
        {bottomLeftContent}
      </IconButton>
      <IconButton sx={{ top: '79.5%', left: '78.8%', ...buttonStyle }} onClick={onBottomRight}>
        {bottomRightContent}
      </IconButton>
      <IconButton sx={{ top: '50.5%', left: '49.5%', ...buttonStyle }} onClick={onCenter}>
        {centerContent}
      </IconButton>
    </Box>
  )
}
