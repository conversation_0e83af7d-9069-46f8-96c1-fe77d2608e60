import PropTypes from 'prop-types';
import { useState, useRef, useEffect } from 'react';
import { IconButton, Stack, Typography, Tooltip, Box } from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import Iconify from '../Iconify';

ScrollablePowerButton.propTypes = {
  onPowerClick: PropTypes.func.isRequired,
  onScheduleClick: PropTypes.func.isRequired,
  powerLabel: PropTypes.string,
  scheduleLabel: PropTypes.string,
  sx: PropTypes.object,
  disabled: PropTypes.bool,
  powerIcon: PropTypes.string,
  scheduleIcon: PropTypes.string,
  powerColor: PropTypes.string,
  scheduleColor: PropTypes.string,
  showScheduleIndicator: PropTypes.bool,
};

const buttonStyle = {
  position: 'absolute',
  transform: 'translate(-50%,-50%)',
  maxWidth: '31%',
  maxHeight: '31%',
  minWidth: '31%',
  minHeight: '31%',
  color: 'grey.100',
  cursor: 'pointer',
  userSelect: 'none',
};

// Animation variants for icon transformation
const iconVariants = {
  power: {
    scale: 1,
    rotate: 0,
    transition: { duration: 0.3, ease: 'easeInOut' }
  },
  schedule: {
    scale: 1.1,
    rotate: 360,
    transition: { duration: 0.5, ease: 'easeInOut' }
  }
};

// Animation variants for the button container
const containerVariants = {
  power: {
    backgroundColor: 'transparent',
    transition: { duration: 0.3 }
  },
  schedule: {
    backgroundColor: 'rgba(255, 193, 7, 0.1)', // Subtle yellow background for alarm mode
    transition: { duration: 0.3 }
  }
};

export default function ScrollablePowerButton({
  onPowerClick,
  onScheduleClick,
  powerLabel = 'Power',
  scheduleLabel = 'Schedule',
  sx = {},
  disabled = false,
  powerIcon = 'carbon:flash-off',
  scheduleIcon = 'material-symbols:alarm',
  powerColor = 'red',
  scheduleColor = 'warning.main',
  showScheduleIndicator = true,
  ...other
}) {
  const [isTransformed, setIsTransformed] = useState(false);
  const resetTimeoutRef = useRef(null);

  useEffect(() => {
    console.log('ScrollablePowerButton: showScheduleIndicator =', showScheduleIndicator);
  }, [showScheduleIndicator]);

  // Clear reset timeout
  const clearResetTimeout = () => {
    if (resetTimeoutRef.current) {
      clearTimeout(resetTimeoutRef.current);
      resetTimeoutRef.current = null;
    }
  };

  // Set reset timeout
  const setResetTimeout = () => {
    clearResetTimeout();
    resetTimeoutRef.current = setTimeout(() => {
      setIsTransformed(false);
    }, 8000); // 8 seconds auto-reset
  };

  // Handle schedule indicator click
  const handleScheduleIndicatorClick = (event) => {
    event.preventDefault();
    event.stopPropagation();

    if (disabled) return;

    setIsTransformed(true);
    setResetTimeout();
  };

  // Handle main button click
  const handleButtonClick = () => {
    if (disabled) return;

    if (isTransformed) {
      onScheduleClick();
    } else {
      onPowerClick();
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearResetTimeout();
    };
  }, []);

  const currentIcon = isTransformed ? scheduleIcon : powerIcon;
  const currentLabel = isTransformed ? scheduleLabel : powerLabel;
  const currentColor = isTransformed ? scheduleColor : powerColor;



  return (
    <Box sx={{ position: 'relative', display: 'inline-block' }}>
      {/* Schedule Indicator Arrow - positioned above the button */}
      {!isTransformed && showScheduleIndicator && (
        <Tooltip title="Click to access scheduler" placement="top" arrow>
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{
              opacity: [0.6, 1, 0.6],
              y: [0, -3, 0],
              scale: [1, 1.1, 1]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut'
            }}
            style={{
              position: 'absolute',
              top: '-35px',
              left: '50%',
              transform: 'translateX(-50%)',
              zIndex: 2000, // Increased z-index
              cursor: 'pointer',
              pointerEvents: 'auto'
            }}
            onClick={handleScheduleIndicatorClick}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: 0.5
              }}
            >
              {/* Upward arrow with glow effect */}
              <Box
                sx={{
                  width: 0,
                  height: 0,
                  borderLeft: '8px solid transparent',
                  borderRight: '8px solid transparent',
                  borderBottom: '12px solid #ffc107',
                  filter: 'drop-shadow(0 0 6px rgba(255, 193, 7, 0.8))',
                  '&:hover': {
                    filter: 'drop-shadow(0 0 12px rgba(255, 193, 7, 1))',
                    borderBottomColor: '#ffb300'
                  }
                }}
              />
              {/* Small text label */}
              <Typography
                variant="caption"
                sx={{
                  fontSize: '0.6rem',
                  color: '#ffc107',
                  fontWeight: 600,
                  textShadow: '0 0 4px rgba(255, 193, 7, 0.4)',
                  userSelect: 'none'
                }}
              >
                Schedule
              </Typography>
            </Box>
          </motion.div>
        </Tooltip>
      )}

      {/* Main Power Button */}
      <Tooltip
        title={isTransformed ? "Click to schedule commands" : currentLabel}
        placement="bottom"
        arrow
      >
        <motion.div
          variants={containerVariants}
          animate={isTransformed ? 'schedule' : 'power'}
          style={{
            position: 'relative',
            borderRadius: '50%',
            padding: '4px',
          }}
        >
          <IconButton
            sx={{
              ...buttonStyle,
              ...sx,
              opacity: disabled ? 0.5 : 1,
              '&:hover': {
                backgroundColor: isTransformed
                  ? 'rgba(255, 193, 7, 0.2)'
                  : 'rgba(255, 0, 0, 0.1)',
              }
            }}
            onClick={handleButtonClick}
            disabled={disabled}
            {...other}
          >
            <Stack alignItems="center" justifyContent="center">
              <AnimatePresence mode="wait">
                <motion.div
                  key={isTransformed ? 'schedule' : 'power'}
                  variants={iconVariants}
                  initial="power"
                  animate={isTransformed ? 'schedule' : 'power'}
                  exit="power"
                >
                  <Iconify
                    icon={currentIcon}
                    width={28}
                    height={28}
                    sx={{ color: currentColor }}
                  />
                </motion.div>
              </AnimatePresence>
              <Typography
                variant="caption"
                sx={{
                  color: currentColor,
                  fontWeight: isTransformed ? 600 : 400,
                  fontSize: isTransformed ? '0.7rem' : '0.75rem'
                }}
              >
                {currentLabel}
              </Typography>
            </Stack>
          </IconButton>
        </motion.div>
      </Tooltip>
    </Box>
  );
}
