import { useState, useEffect } from "react";
import {
  Container,
  Typography,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
  TextField,
  Grid,
  Card,
  CardContent,
  Snackbar,
  Alert,
} from "@mui/material";
import { styled } from "@mui/system";
import axios from "../../utils/axios";
import AddIcon from "@mui/icons-material/Add";
import SearchIcon from "@mui/icons-material/Search";
import VisibilityIcon from "@mui/icons-material/Visibility";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";
import Page from "../../components/Page";
import Layout from "../../layout";

// A styled Container (optional for quick theming changes)
const StyledContainer = styled(Container)(({ theme }) => ({
  paddingTop: theme.spacing(12),
  display: "flex",
  flexDirection: "column",
  gap: theme.spacing(2),
}));

export default function UserManage() {
  const [stats, setStats] = useState({
    totalUsers: 0,
    expiredUsers: 0,
    notExpiredUsers: 0,
    todayExpiringUsers: 0,
  });
  const [searchResults, setSearchResults] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResponse, setSearchResponse] = useState(null);

  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);

  const [openDeleteDeviceDialog, setOpenDeleteDeviceDialog] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState(null);

  // New states for Extend License
  const [extendUser, setExtendUser] = useState(null);
  const [openExtendDialog, setOpenExtendDialog] = useState(false);
  const [expiredDate, setExpiredDate] = useState("");
  const [licenseKey, setLicenseKey] = useState("");

  const [notification, setNotification] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [showPincode, setShowPincode] = useState({});

  const togglePincodeVisibility = (userId) => {
    setShowPincode(prev => ({
      ...prev,
      [userId]: !prev[userId]
    }));
  };

  const handleAddUserSubmit = async (event) => {
    event.preventDefault();
    const phoneNumber = event.target.phoneNumber.value.trim();
    if (!phoneNumber) return;
    try {
      const response = await axios.post("/api/auth/register", { phoneNumber });
      if (response.data.success) {
        setNotification({ open: true, message: "User added successfully", severity: "success" });
        // Optionally refresh stats or search results if needed
      } else {
        setNotification({ open: true, message: response.data.message || "Failed to add user", severity: "error" });
      }
    } catch (error) {
      console.error("Registration error:", error);
      setNotification({ open: true, message: "Registration error", severity: "error" });
    }
  };

  const handleDeleteUser = (phoneNumber) => {
    axios
      .post("/api/admin/user/delete", { phoneNumber })
      .then((res) => {
        if (res.data.success) {
          console.log("User successfully deleted");
          // Optionally refresh stats or search results if needed
        } else {
          console.log(res.data.message || "Failed to delete user");
        }
      })
      .catch((err) => {
        console.error("Error deleting user:", err);
      });
  };

  const handleDeleteDevice = (deviceNumber) => {
    axios
      .post("/api/device/delete", { deviceNumber })
      .then((res) => {
        if (res.data.success) {
          console.log("Device successfully deleted");
          // Optionally refresh stats or search results if needed
        } else {
          console.log(res.data.message || "Failed to delete device");
        }
      })
      .catch((err) => {
        console.error("Error deleting device:", err);
      });
  };

  // New function to handle license extension
  const handleExtendLicense = async () => {
    if (!extendUser || !expiredDate) {
      return;
    }
    try {
      const response = await axios.post("/api/admin/user/extend-license", {
        expired: expiredDate,
        user: extendUser._id,
        licenseKey,
      });
      if (response.data.success) {
        setNotification({
          open: true,
          message: "License extended successfully",
          severity: "success",
        });
      } else {
        setNotification({
          open: true,
          message: response.data.message || "Failed to extend license",
          severity: "error",
        });
      }
    } catch (err) {
      console.error("Error extending license:", err);
      setNotification({
        open: true,
        message: "Internal error extending license",
        severity: "error",
      });
    } finally {
      setOpenExtendDialog(false);
      setExtendUser(null);
      setExpiredDate("");
      setLicenseKey("");
    }
  };

  const handleSearch = () => {
    if (!searchQuery.trim()) return;
    console.log("Search Query:", searchQuery);

    // Decide whether to treat the query as phoneNumber or deviceNumber based on length
    const paramObj = {};
    if (searchQuery.length >= 14) {
      paramObj.deviceNumber = searchQuery;
    } else {
      paramObj.phoneNumber = searchQuery;
    }

    axios
      .get("/api/admin/user/list", { params: paramObj })
      .then((res) => {
        if (res.data.success) {
          // Instead of using _id, let's remove duplicates by comparing phoneNumber
          const uniqueUsers = res.data.users.reduce((acc, user) => {
            if (!acc.some((u) => u.phoneNumber === user.phoneNumber)) {
              acc.push(user);
            }
            return acc;
          }, []);

          setSearchResults(uniqueUsers);
          setSearchResponse({ ...res.data, users: uniqueUsers });
        }
      })
      .catch((err) => {
        console.error("Error searching users:", err);
      });
  };

  useEffect(() => {
    axios
      .get("/api/admin/user/list")
      .then((res) => {
        if (res.data.success) {
          setStats({
            totalUsers: res.data.totalUsers,
            expiredUsers: res.data.expiredUsers,
            notExpiredUsers: res.data.notExpiredUsers,
            todayExpiringUsers: res.data.todayExpiringUsers,
          });
        }
      })
      .catch((err) => {
        console.error("Error loading users:", err);
      });
  }, []);

  return (
    <Page title="User Manage">
      <StyledContainer>
        <Layout />

        {/* Extend License Dialog */}
        <Dialog
          open={openExtendDialog}
          onClose={() => {
            setOpenExtendDialog(false);
            setExtendUser(null);
            setExpiredDate("");
            setLicenseKey("");
          }}
        >
          <DialogTitle>Extend License</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Extend license for user: {extendUser?.phoneNumber}
            </DialogContentText>
            <TextField
              fullWidth
              type="date"
              label="New Expiration"
              InputLabelProps={{ shrink: true }}
              value={expiredDate}
              onChange={(e) => setExpiredDate(e.target.value)}
              sx={{ mt: 2 }}
            />
            <TextField
              fullWidth
              label="License Key (optional)"
              value={licenseKey}
              onChange={(e) => setLicenseKey(e.target.value)}
              sx={{ mt: 2 }}
            />
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => {
                setOpenExtendDialog(false);
                setExtendUser(null);
                setExpiredDate("");
                setLicenseKey("");
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleExtendLicense} color="primary">
              Extend
            </Button>
          </DialogActions>
        </Dialog>

        <Card>
          <CardContent>
            <Grid container spacing={2} justifyContent="center" alignItems="flex-end">
              <Grid item xs={12} sm="auto">
                <TextField
                  fullWidth
                  variant="outlined"
                  label="Search by phone or device number"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid>
              <Grid item>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleSearch}
                  startIcon={<SearchIcon />}
                >
                  Search
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        <Card sx={{ mt: 2 }}>
          <CardContent>
            <Box
              component="form"
              onSubmit={handleAddUserSubmit}
              sx={{ display: "flex", alignItems: "center", gap: 2 }}
            >
              <TextField
                fullWidth
                variant="outlined"
                label="New user phone number"
                name="phoneNumber"
              />
              <IconButton
                type="submit"
                color="primary"
                aria-label="add user"
              >
                <AddIcon />
              </IconButton>
            </Box>
          </CardContent>
        </Card>

        <Card sx={{ mt: 2 }}>
          <CardContent>
            <Box
              display="grid"
              gridTemplateColumns="repeat(auto-fit, minmax(130px, 1fr))"
              gap={2}
              textAlign="center"
            >
              <Typography variant="body2" color="text.secondary">
                Total: {stats.totalUsers}
              </Typography>
              <Typography variant="body2" sx={{ color: "error.main" }}>
                Expired: {stats.expiredUsers}
              </Typography>
              <Typography variant="body2" sx={{ color: "success.main" }}>
                Active: {stats.notExpiredUsers}
              </Typography>
              <Typography variant="body2" sx={{ color: "warning.main" }}>
                Today Expired: {stats.todayExpiringUsers}
              </Typography>
            </Box>
          </CardContent>
        </Card>

        {searchResponse && searchResponse.users?.length > 0 && (
          <TableContainer
            component={Paper}
            sx={{ marginTop: 2, overflowX: "auto" }}
          >
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Phone</TableCell>
                  <TableCell>Days</TableCell>
                  <TableCell>Device</TableCell>
                  <TableCell>Last Log</TableCell>
                  <TableCell>Ver</TableCell>
                  <TableCell>Sim</TableCell>
                  <TableCell>Pincode</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {searchResponse.users.map((user) => {
                  const rawPayload =
                    searchResponse.lastPayload?.find((lp) => lp._id === user._id) ||
                    {};
                  let parsedPayload = {};
                  if (typeof rawPayload.lastPayload === "string") {
                    try {
                      parsedPayload = JSON.parse(rawPayload.lastPayload);
                    } catch (e) {
                      // fallback
                    }
                  }
                  const simLogData =
                    searchResponse.lastSimcardLog?.find((s) => s._id === user._id) ||
                    {};
                  let simDate = "";
                  let simBalance = "";
                  if (typeof simLogData.lastSimcardLog === "string") {
                    const match = simLogData.lastSimcardLog.match(
                      /([\d.]+).*?(\d{4}\/\d{2}\/\d{2})/
                    );
                    if (match) {
                      simBalance = match[1];
                      simDate = match[2];
                    }
                  }

                  // Debug: Log user data to see what fields are available
                  console.log('User data:', user);

                  return (
                    <TableRow key={user._id}>
                      {/* Clicking phone => delete user */}
                      <TableCell
                        sx={{ cursor: "pointer" }}
                        onClick={() => {
                          setSelectedUser(user);
                          setOpenDeleteDialog(true);
                        }}
                      >
                        {user.phoneNumber}
                      </TableCell>
                      {/* Clicking Days => extend license */}
                      <TableCell
                        sx={{ cursor: "pointer" }}
                        onClick={() => {
                          setExtendUser(user);
                          setOpenExtendDialog(true);
                        }}
                      >
                        {Math.ceil(
                          (new Date(user.expired) - new Date()) / (1000 * 60 * 60 * 24)
                        )}
                      </TableCell>
                      {/* Clicking device => delete device */}
                      <TableCell
                        sx={{ cursor: "pointer" }}
                        onClick={() => {
                          if (user.devices?.deviceNumber) {
                            setSelectedDevice(user.devices.deviceNumber);
                            setOpenDeleteDeviceDialog(true);
                          }
                        }}
                      >
                        {user.devices?.deviceNumber}{" "}
                        {user.devices?.createdAt
                          ? `(${new Date(user.devices.createdAt).toLocaleString()})`
                          : ""}
                      </TableCell>
                      <TableCell>{rawPayload.lastPayloadCreatedAt || ""}</TableCell>
                      <TableCell>{parsedPayload.ver}</TableCell>
                      <TableCell>
                        {simDate} - {simBalance}
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography
                            variant="body2"
                            sx={{
                              fontFamily: 'monospace',
                              minWidth: '60px',
                              letterSpacing: showPincode[user._id] ? '0.1em' : '0.3em'
                            }}
                          >
                            {showPincode[user._id] ? (user.pinCode || user.pin || user.pincode || user.PIN || 'No PIN') : '••••'}
                          </Typography>
                          <IconButton
                            size="small"
                            onClick={() => togglePincodeVisibility(user._id)}
                            sx={{ p: 0.5 }}
                          >
                            {showPincode[user._id] ? (
                              <VisibilityOffIcon fontSize="small" />
                            ) : (
                              <VisibilityIcon fontSize="small" />
                            )}
                          </IconButton>
                        </Box>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        )}

        {/* Delete user dialog */}
        <Dialog
          open={openDeleteDialog}
          onClose={() => setOpenDeleteDialog(false)}
        >
          <DialogTitle>Delete User</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Are you sure you want to delete {selectedUser?.phoneNumber}?
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenDeleteDialog(false)}>Cancel</Button>
            <Button
              onClick={() => {
                if (selectedUser) {
                  handleDeleteUser(selectedUser.phoneNumber);
                }
                setOpenDeleteDialog(false);
              }}
              color="error"
            >
              Delete
            </Button>
          </DialogActions>
        </Dialog>

        {/* Delete device dialog */}
        <Dialog
          open={openDeleteDeviceDialog}
          onClose={() => setOpenDeleteDeviceDialog(false)}
        >
          <DialogTitle>Delete Device</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Are you sure you want to delete device {selectedDevice}?
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenDeleteDeviceDialog(false)}>Cancel</Button>
            <Button
              onClick={() => {
                if (selectedDevice) {
                  handleDeleteDevice(selectedDevice);
                }
                setOpenDeleteDeviceDialog(false);
              }}
              color="error"
            >
              Delete
            </Button>
          </DialogActions>
        </Dialog>

        {/* Notification Snackbar */}
        <Snackbar
          open={notification.open}
          autoHideDuration={3000}
          onClose={() => setNotification({ ...notification, open: false })}
          anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        >
          <Alert
            severity={notification.severity}
            onClose={() => setNotification({ ...notification, open: false })}
          >
            {notification.message}
          </Alert>
        </Snackbar>

      </StyledContainer>
    </Page>
  );
}
