import { useState, useEffect } from "react";
import { useSnackbar } from "notistack";
import { motion } from "framer-motion";

// @mui
import SatelliteAltIcon from "@mui/icons-material/SatelliteAlt";

import {
  Card,
  Container,
  Grid,
  Box,
  Stack,
  Typography,
  Divider,
  Alert,
} from "@mui/material";
import "leaflet/dist/leaflet.css";
import { useTranslation } from "react-i18next";
import SeasonalAnimation from "../components/SeasonalAnimation";
// pusher
import useSound from "use-sound";

import io from "socket.io-client";

import engine from "../assets/engine.mp3";
import lock from "../assets/lock.mp3";
// components
import Page from "../components/Page";
import Car from "../components/Car";

import ChipIcon from "../components/ChipIconi";

import Iconify from "../components/Iconify";

import FindingScreen from "../components/FindingScreen";
// hooks
import useResponsive from "../hooks/useResponsive";
import useAuth from "../hooks/useAuth";
import useLocalStorage from "../hooks/useLocalStorage";
import Layout from "../layout";
import axios from "../utils/axios";
import CarLocation from "./CarLocation";
import {  HOST_API } from "../config";
import PinCodeConfirm from "../layout/PinCodeConfirm";

import { fShortenNumber } from "../utils/formatUtils";
import DeviceController from "../components/DeviceController";
import CarSide from "../components/CarSide";
import { useNavigate } from "react-router";
import WeatherDisplay from "../layout/weather";
import { InfoOutlined } from "@mui/icons-material";
import PinCodeSetupDialog from "../components/PinCodeSetupDialog";
import ScrollablePowerButton from "../components/ScrollablePowerButton";
import SchedulerDialog from "../components/SchedulerDialog";
// mqtt
import mqttService from "../services/mqttService";
export default function Home() {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("initializing...");
  const [description, setDescription] = useState("");
  const { initialize, user } = useAuth();
  const [showPincodeSetup, setShowPincodeSetup] = useState(false);
  const status = null;
  const [action, setAction] = useState("");
  const [deviceStatus, setDeviceStatus] = useState();
  const [color, setColor] = useState("gray");
  const [viewMap, setViewMap] = useState(false);

  const [ setMotionTime] = useState();
  const { enqueueSnackbar } = useSnackbar();

  const [deviceNumber] = useState(user?.device?.deviceNumber);
  const [checkOnline, setCheckOnline] = useState(false);
  const [command, setCommand] = useState("check");
  const [checkedPincode, setCheckedPincode] = useState(true);
  const [iconChange, setChangeicon] = useState(false);
  const [online, setOnline] = useState(false);
  const { t } = useTranslation();
  const [play] = useSound(engine);
  const navigate = useNavigate();

  // Countdown timer states - REMOVED for better bird animation
  const [countdown, setCountdown] = useState(20);
  const [isTimerVisible, setTimerVisible] = useState(false);
  const [simStatus, setSimStatus] = useState({ balance: '', expiredDate: '' });

  // Protocol and MQTT states
  const [protocol] = useLocalStorage(`device_${user?.device?.deviceNumber}_protocol`, 'tcp');
  const [mqttConnected, setMqttConnected] = useState(false);
  const [mqttConnecting, setMqttConnecting] = useState(false);

  // Toggle button states
  const [isLocked, setIsLocked] = useState(true); // Assume locked by default
  const [isEngineOn, setIsEngineOn] = useState(false); // Assume engine off by default

  // Engine button hold animation states
  const [isEngineHolding, setIsEngineHolding] = useState(false);
  const [engineHoldProgress, setEngineHoldProgress] = useState(0);
  const [engineHoldTimer, setEngineHoldTimer] = useState(null);

  // Button position for bird animation
  const [buttonPosition, setButtonPosition] = useState(null);

  // Button bouncing animation states
  const [bouncingButton, setBouncingButton] = useState(null); // 'power', 'lock', 'location', or null

  // Scheduler dialog state
  const [schedulerDialogOpen, setSchedulerDialogOpen] = useState(false);

  const fetchSimStatus = async () => {
    if (deviceNumber) {
      try {
        const url = `/api/log/sim-status?deviceNumber=${deviceNumber}`;
        const response = await axios.get(url);
        if (response.data.success) {
          const fetchedData = response.data.data;
          const dateParts = fetchedData.expiredDate.split('/'); // Split by '/'
          const formattedExpiredDate = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`; // Convert to YYYY-MM-DD
          setSimStatus({
            balance: fetchedData.balance,
            expiredDate: formattedExpiredDate
          });
          // console.log("Fetched SIM status:", fetchedData); // Log fetched SIM status
        }
      } catch (error) {
        // console.error('Error fetching SIM status:', error);
      }
    } else {
      // console.log("deviceNumber is not set");
    }
  };

  const checkSimcard = async () => {
    try {
      const headers = {
        'Content-Type': 'application/json; charset=UTF-8',
      };

      const response = await axios.post(`/api/device/check-sim`, {
        deviceNumber: user.device?.deviceNumber
      }, { headers });

      if (response.status === 200) {
        // console.log("checksim:", response.data);
      }
    } catch {
      // console.error('Error:', error);
    }
  };

  // MQTT connection function
  const connectToMqttBroker = async () => {
    if (protocol !== 'tcp' || !user?.device?.deviceNumber) {
      return;
    }

    try {
      // Get broker address from user device or use fallback
      const brokerAddress = user?.device?.renter || "elec.mn";
      const brokerUrl = `wss://${brokerAddress}:8084/mqtt`;

      // console.log(`MQTT: Connecting to broker for LwM2M protocol: ${brokerUrl}`);
      setMqttConnecting(true);
      setMqttConnected(false); // Ensure we start with disconnected state

      const connectionStarted = mqttService.connect(brokerUrl, {
        clean: true,
        keepalive: 60,
        reconnectPeriod: 1000,
        connectTimeout: 30000,
        clientId: `home_${user.device.deviceNumber}_${Math.random().toString(16).substring(2, 10)}`,
      });

      if (connectionStarted) {
        // Set up MQTT event listeners
        mqttService.on('connect', () => {
          // console.log('MQTT: Connected successfully for LwM2M protocol');
          setMqttConnected(true);
          setMqttConnecting(false);

          // Subscribe to device message topic (correct format: deviceNumber/msg)
          const responseTopic = `${user.device.deviceNumber}/msg`;
          mqttService.subscribe(responseTopic);
          // console.log(`MQTT: Subscribed to ${responseTopic}`);

          // Automatically send check command when MQTT connection is established
          setTimeout(() => {
            // console.log('MQTT: Sending automatic check command after connection');
            const checkMessage = JSON.stringify({
              id: user.device.deviceNumber,
              command: "check"
            });

            const success = mqttService.publish(user.device.deviceNumber, checkMessage);
            if (success) {
              // console.log('MQTT: Automatic check command sent successfully');
              setAction("check");
              setDescription("Getting device status...");
            } else {
              // console.error('MQTT: Failed to send automatic check command');
            }
          }, 1000); // Wait 1 second after connection to ensure everything is ready
        });

        mqttService.on('message', (topic, message) => {
          // console.log(`MQTT: Received message on ${topic}: ${message}`);

          // Handle device response messages (format: deviceNumber/msg)
          if (topic.includes('/msg')) {
            try {
              const data = JSON.parse(message);
              // console.log('MQTT: Parsed device data:', data);

              // Convert the MQTT response format to the expected displayData format
              // MQTT format: {"Lon":"106.88213 E","Lat":"47.93462 N","hum":10,"ver":"1.3.9","rssi":30,"volt":12.4773,"Speed":0,"temp":22,"motion":0,"light":0}
              // Expected format: payload as JSON string

              // Parse coordinates properly to ensure they are numbers
              let latitude = data.Lat;
              let longitude = data.Lon;

              // Handle string coordinates with directional indicators
              if (typeof data.Lat === 'string') {
                latitude = parseFloat(data.Lat.replace(/[^\d.-]/g, ''));
                // Ensure it's a valid number
                if (isNaN(latitude)) {
                  latitude = 0;
                }
              }
              if (typeof data.Lon === 'string') {
                longitude = parseFloat(data.Lon.replace(/[^\d.-]/g, ''));
                // Ensure it's a valid number
                if (isNaN(longitude)) {
                  longitude = 0;
                }
              }

              // console.log('MQTT: Parsed coordinates - Lat:', latitude, 'Lon:', longitude, 'Types:', typeof latitude, typeof longitude);

              const payload = JSON.stringify({
                volt: data.volt,
                temp: data.temp,
                hum: data.hum,
                Lat: latitude,
                Lon: longitude,
                motion: data.motion,
                light: data.light,
                sta: data.volt >= 13.5 ? 1 : 0, // Determine engine status based on voltage
                Speed: data.Speed,
                ver: data.ver,
                rssi: data.rssi
              });

              // Process the response similar to socket.io data-received
              displayData({
                payload: payload,
                ts: new Date(),
                from_client_id: user.device.deviceNumber
              });
            } catch {
              // console.error('MQTT: Error parsing response message:', error);
            }
          }
        });

        mqttService.on('disconnect', () => {
          // console.log('MQTT: Disconnected');
          setMqttConnected(false);
          setMqttConnecting(false);
        });

        mqttService.on('error', () => {
          // console.error('MQTT: Connection error:', error);
          setMqttConnected(false);
          setMqttConnecting(false);

          // Show user-friendly error message
          enqueueSnackbar('Connection failed - please check your network', { variant: 'error' });
        });
      } else {
        // console.error('MQTT: Failed to start connection');
        setMqttConnecting(false);
        enqueueSnackbar('Failed to connect to device', { variant: 'error' });
      }
    } catch {
      // console.error('MQTT: Failed to connect:', error);
      setMqttConnected(false);
      setMqttConnecting(false);
      enqueueSnackbar('Connection error - please try again', { variant: 'error' });
    }
  };

  // MQTT command sending function
  const sendMqttCommand = (command) => {
    if (!mqttConnected || !mqttService.isConnected) {
      // console.error('MQTT: Cannot send command, not connected');

      // If MQTT is connecting, show connecting status instead of error
      if (mqttConnecting && protocol === 'tcp' && user?.device?.deviceNumber) {
        // console.log('MQTT: Connection in progress, queuing command...');
        setAction(command);
        setDescription(`Connecting to your device...`);

        // Set up a one-time listener for connection success
        const handleConnectionSuccess = () => {
          // console.log('MQTT: Connection established, sending queued command');
          mqttService.off('connect', handleConnectionSuccess);
          // Retry the command now that we're connected
          setTimeout(() => sendMqttCommand(command), 500);
        };

        mqttService.on('connect', handleConnectionSuccess);

        // Set up a timeout for the connection attempt (use longer timeout)
        setTimeout(() => {
          if (!mqttConnected) {
            mqttService.off('connect', handleConnectionSuccess);
            enqueueSnackbar('Connection timeout - please check your connection', { variant: 'error' });
            setDescription('');
          }
        }, 15000); // 15 second timeout instead of 2 seconds

        return true; // Return true to prevent immediate error
      }

      // If not connecting, show appropriate error or attempt to reconnect
      if (protocol === 'tcp') {
        // console.log('MQTT not connected for LwM2M protocol, attempting to reconnect...');
        // Try to reconnect
        connectToMqttBroker();
        enqueueSnackbar('Connecting to device, please try again in a moment', { variant: 'info' });
      } else {
        enqueueSnackbar('Device not available for this protocol', { variant: 'warning' });
      }
      return false;
    }

    try {
      const topic = user.device.deviceNumber;
      const message = JSON.stringify({
        id: user.device.deviceNumber,
        command: command
      });

      // console.log(`MQTT: Sending command ${command} to topic ${topic}`);
      // console.log(`MQTT: Message: ${message}`);

      const success = mqttService.publish(topic, message);

      if (success) {
        setAction(command);
        setDescription(`Sending command to your device...`);
        // setTimerVisible(true); // Removed for better bird animation

        if (command.includes("lock")) {
          const lockSound = new Audio(lock);
          lockSound.play();
        }

        return true;
      } else {
        enqueueSnackbar('Failed to send command to device', { variant: 'error' });
        return false;
      }
    } catch {
      // console.error('MQTT: Error sending command:', error);
      enqueueSnackbar('Error sending command to device', { variant: 'error' });
      return false;
    }
  };



  // Toggle-style handlers
  const handleLockToggle = (event) => {
    // Capture button position for bird animation
    if (event && event.currentTarget) {
      const rect = event.currentTarget.getBoundingClientRect();
      setButtonPosition({
        x: rect.left + rect.width / 2,
        y: rect.top + rect.height / 2
      });
    }

    // Start bouncing animation
    setBouncingButton('lock');
    setTimeout(() => setBouncingButton(null), 3000); // Stop bouncing after 3 seconds

    if (isLocked) {
      setMessage(t("home.door_open"));
      handleDeviceControl("unlock");
      setIsLocked(false); // Update state immediately for UI responsiveness
    } else {
      setMessage(t("home.door_close"));
      handleDeviceControl("lock");
      setIsLocked(true); // Update state immediately for UI responsiveness
    }
  };

  // Engine button hold handlers
  const handleEngineMouseDown = (event) => {
    // Prevent default to avoid any interference
    event.preventDefault();

    // Capture button position for bird animation
    const rect = event.currentTarget.getBoundingClientRect();
    setButtonPosition({
      x: rect.left + rect.width / 2,
      y: rect.top + rect.height / 2
    });

    setIsEngineHolding(true);
    setEngineHoldProgress(0);

    const startTime = Date.now();
    const duration = 3000; // 3 seconds

    const timer = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      setEngineHoldProgress(progress * 100);

      if (progress >= 1) {
        // Hold completed - execute command
        clearInterval(timer);
        setIsEngineHolding(false);
        setEngineHoldProgress(0);

        // Start bouncing animation
        setBouncingButton('power');
        setTimeout(() => setBouncingButton(null), 3000); // Stop bouncing after 3 seconds

        if (isEngineOn) {
          setMessage(t("home.turn_off"));
          handleDeviceControl("untar");
          setIsEngineOn(false);
        } else {
          setMessage(t("home.turn_on"));
          handleDeviceControl("as");
          setIsEngineOn(true);
        }
      }
    }, 16); // ~60fps

    setEngineHoldTimer(timer);
  };

  const handleEngineMouseUp = (event) => {
    // Prevent default to avoid any interference
    if (event) {
      event.preventDefault();
    }

    // Cancel hold if released early
    if (engineHoldTimer) {
      clearInterval(engineHoldTimer);
      setEngineHoldTimer(null);
    }
    setIsEngineHolding(false);
    setEngineHoldProgress(0);
  };



  const handleLocationToggle = (event) => {
    // Capture button position for bird animation
    if (event && event.currentTarget) {
      const rect = event.currentTarget.getBoundingClientRect();
      setButtonPosition({
        x: rect.left + rect.width / 2,
        y: rect.top + rect.height / 2
      });
    }

    // Start bouncing animation
    setBouncingButton('location');
    setTimeout(() => setBouncingButton(null), 3000); // Stop bouncing after 3 seconds

    if (viewMap) {
      // Hide location
      setViewMap(false);
    } else {
      // Show location - get current status first
      handleLocation(true);
    }
  };




  const handleOn1 = () => {
    setMessage(t("home.on1"));
    handleDeviceControl("on1");
  };
  const handleOn2 = () => {
    setMessage(t("home.on2"));
    handleDeviceControl("on2");
  };
  const handleOff1 = () => {
    setMessage(t("home.off1"));
    handleDeviceControl("off1");
  };
  const handleOff2 = () => {
    setMessage(t("home.off2"));
    handleDeviceControl("off2");
  };

  // Handlers for scrollable power button
  const handlePowerClick = () => {
    handleOff2(); // Keep the original power functionality
  };

  const handleScheduleClick = () => {
    setSchedulerDialogOpen(true);
  };

  const handleSchedulerClose = () => {
    setSchedulerDialogOpen(false);
  };

  const handleLocation = (mapView = true) => {
    if (user.status === "expired") {
      enqueueSnackbar(t("words.expired_message"), { variant: "error" });
      return false;
    }

    // For LwM2M protocol, check if MQTT is connected before proceeding
    if (protocol === 'tcp' && !mqttConnected && !mqttConnecting) {
      // console.log('MQTT not connected for LwM2M protocol, attempting to connect first...');
      connectToMqttBroker();
      return false;
    }

    if (!viewMap) {
      setMessage(t("words.get_status"));
      setCommand("check");
      confirmed("check");
      if (mapView) {
        setTimeout(() => {
          setViewMap(!viewMap);
        }, 3000);
      }
    } else setViewMap(!viewMap);
  };
  const displayData = (data) => {
    const payloadString = data.payload.replace(" N", "").replace(" E", "");

    if (isJson(payloadString)) {
      const payload = JSON.parse(payloadString);
      const ts = new Date(data.ts);
      if (payload.motion === 1) {
        setMotionTime(`${ts.getHours()}:${ts.getMinutes()}:${ts.getSeconds()}`);
      }
      if (payload.sta === 0) {
        setColor("red");
      }
      if (payload.sta === 1) {
        setColor("green");
        const engineSound = new Audio(engine);
        engineSound.play().catch(() => {
          // Audio play failed - user interaction required
        });
      }

      if (payload.volt >= 13.5) {
         const engineSound = new Audio(engine);
         engineSound.play().catch(() => {
           // Audio play failed - user interaction required
         });
         setColor("green");
      } else {
         setColor("gray");
      }

      const carStatus = {
        volt: payload.volt,
        temp: payload.temp,
        hum: payload.hum,
        Lat: typeof payload.Lat === 'string' ? parseFloat(payload.Lat) : payload.Lat,
        Lon: typeof payload.Lon === 'string' ? parseFloat(payload.Lon) : (payload.Lon || payload.lon),
        motion: payload.motion,
        light: payload.light,
        sta: payload.sta,
        speed: payload.Speed,
        ver: payload.ver,
        backlight: payload?.s1 || 0,
        rssi: payload.rssi,
      };
      setDeviceStatus(carStatus);

      // Update toggle states based on device response
      if (payload.sta !== undefined) {
        setIsEngineOn(payload.sta === 1);
      }

      // Update lock state based on recent action
      if (action === "lock") {
        setIsLocked(true);
      } else if (action === "unlock") {
        setIsLocked(false);
      }

      // setTimerVisible(false); // Hide timer when data arrives - REMOVED for better bird animation
    } else {
      const status = data.payload;
      if (status === "aslaa!") {
        setColor("green");
        play();
      } else if (status === "untarlaa!") {
        setColor("red");
      } else {
        // console.log('Something wrong in data');
      }
    }
    // console.log("Incoming data:", data); // Log incoming data
    // console.log("Current color state:", color); // Log current color state
  };

  const isJson = (data) => {
    try {
      JSON.parse(data);
    } catch {
      return false;
    }
    return true;
  };
  const confirmed = (preStatus) => {
    if (
      user.device === null ||
      (user?.status === "trial" && user?.remainDays <= 0)
    ) {
      setCheckedPincode(true);
      setLoading(true);

      setAction(command);
      setDescription(`${command} was success`);

      setTimeout(() => {
        setLoading(false);
        const engineStatus = command === "as" ? 1 : (command === "untar" ? 0 : (isEngineOn ? 1 : 0));
        const payload = JSON.stringify({
          volt: fShortenNumber(Math.random() * 12),
          temp: fShortenNumber(Math.random() * 60),
          hum: fShortenNumber(Math.random() * 50 + 40),
          Lat: 47.918918911176014,
          Lon: 106.91759599276008,
          motion: Math.floor(Math.random()),
          light: Math.floor(Math.random()),
          sta: engineStatus,
          rel1: Math.floor(Math.random()),
          rel2: Math.floor(Math.random()),
          backlight: 1,
        });
        const mockdata = {
          ts: new Date(),
          payload,
        };
        displayData(mockdata);
      }, 3000);
    } else {
      setCheckedPincode(true);
      setLoading(true);
      setDescription("");

      const commandToSend = preStatus || command;
      // console.log("Command being sent:", commandToSend);
      // console.log("Protocol selected:", protocol);

      // Check if LwM2M protocol is selected (tcp)
      if (protocol === 'tcp') {
        // console.log("Using MQTT for LwM2M protocol");

        // Use MQTT for LwM2M protocol
        const success = sendMqttCommand(commandToSend);

        if (success) {
          // MQTT command sent successfully, timer and description are set in sendMqttCommand
          setTimeout(() => {
            setLoading(false);
          }, 3000);
        } else {
          // Command failed, fallback to HTTP or show error
          setLoading(false);
          enqueueSnackbar("Command failed, please check connection", {
            variant: "error",
          });
        }
      } else {
        // console.log("Using HTTP API for XMPP protocol");

        // Use HTTP API for XMPP protocol (original logic)
        // const requestPayload = {
        //   deviceNumber: user?.device?.deviceNumber,
        // };
        // console.log("Sending to API with payload:", requestPayload);

        axios
          .post(`/api/device/control/:${commandToSend}`, {
            deviceNumber: user?.device?.deviceNumber,
          })
          .then((res) => {
            if (res.data.success) {
              setAction(commandToSend);
              setDescription(
                `Command sent successfully, please wait...`
              );
              // setTimerVisible(true); // Show timer when command is sent - REMOVED for better bird animation
              if (command.includes("lock")) {
                const lockSound = new Audio(lock);
                lockSound.play().catch(() => {
                  // Audio play failed - user interaction required
                });
              }
            } else if (res.data.err) {
              setAction("");
              if (typeof res.data.err === "object")
                setDescription(
                  `code = ${res.data.err.code}, host= ${res.data.err.address}, port =  ${res.data.err.port}`
                );
              if (typeof res.data.err === "string") {
                setDescription(res.data.err);
                setLoading(false);
                enqueueSnackbar(res.data.err, { variant: "error" });
              }
            }
            setTimeout(() => {
              setLoading(false);
            }, 3000);
          })
          .catch(() => {
            enqueueSnackbar("Please check your connection or status", {
              variant: "error",
            });
            setLoading(false);
          });
      }
    }
  };

  const handleDeviceControl = (cmd) => {
    if (user.status === "expired") {
      enqueueSnackbar(t("words.expired_message"), { variant: "error" });
      return false;
    }
    setCommand(cmd);

    // Skip pincode verification - directly execute command
    setCheckedPincode(true);
    confirmed(cmd);
  };

  const isMobile = useResponsive("down", "sm");

  useEffect(() => {

    fetchSimStatus();
    let mounted = true;
    if (user) {
      if (user.device) {
        const uix = user.device.uix;
        if (uix.includes("Car")) {
          setChangeicon(false);
        } else if (uix.includes("GPS")) {
          navigate("/log-gps");
        } else {
          setChangeicon(true);
        }
      }

      // Connect to MQTT if LwM2M protocol is selected
      if (protocol === 'tcp' && user.device?.deviceNumber) {
        // console.log('LwM2M protocol detected, connecting to MQTT...');
        // Add a small delay to ensure component is fully mounted
        setTimeout(() => {
          if (mounted) {
            connectToMqttBroker();
          }
        }, 500);
      }

      const socket = io.connect(`${HOST_API}`, {
        transports: ["polling", "websocket"],
      });
      socket.on("connect", () => {
        // console.log('connected to host');
        socket.emit("logined", user);
      });
      socket.on("disconnect", () => {
        // console.log("disconnected from the host");
      });

      if (user?.device?.type === "4g") {
        setCheckOnline(true);
        axios
          .post(`/api/device/checkline`, {
            deviceNumber: user?.device?.deviceNumber,
          })
          .then((res) => {
            if (!mounted) return;
            if (res.data.status === "online") {
              setOnline(true);
              setColor("red");

              // Only call handleLocation for non-LwM2M protocols or when MQTT is connected
              if (protocol !== 'tcp') {
                // For XMPP protocol, use handleLocation immediately
                handleLocation(false);
              } else {
                // For LwM2M protocol, wait for MQTT connection or use a delay
                // console.log('LwM2M protocol detected, skipping immediate location check - will be handled by MQTT auto-check');
              }
            } else {
              setOnline(false);
              setColor("yellow");
            }
          })
          .finally(() => {
            if (!mounted) return;
            setCheckOnline(false);
          });
      }

      socket.on("data-received", (data) => {
        if (!mounted) return;
        if (data.payload && data.from_client_id === user.device.deviceNumber) {
          displayData(data);
        }
      });

      if (status !== null && status.sta >= 0) {
        setDeviceStatus(status);

        if (status.sta === 1) {
          setColor("green");
          setIsEngineOn(true);
        } else {
          setIsEngineOn(false);
        }
      }

      return () => {
        try {
          mounted = false;
          if (socket.disconnect()) {
            socket.close();
          }
          // Disconnect MQTT if connected
          if (mqttConnected && mqttService.isConnected) {
            mqttService.disconnect();
            setMqttConnected(false);
          }
          // Clear engine hold timer if active
          if (engineHoldTimer) {
            clearInterval(engineHoldTimer);
            setEngineHoldTimer(null);
          }
        } catch (err) {
          // console.log(err);
        }
      };

    }
  }, [deviceNumber, protocol]);

  // Countdown timer effect - REMOVED for better bird animation
  // useEffect(() => {
  //   if (isTimerVisible) {
  //     const timer = setInterval(() => {
  //       setCountdown((prev) => {
  //         if (prev <= 1) {
  //           clearInterval(timer);
  //           return 0;
  //         }
  //         return prev - 1;
  //       });
  //     }, 1000);
  //     return () => clearInterval(timer);
  //   }
  // }, [isTimerVisible]);

  const getSimCardColor = () => {
    if (!simStatus.expiredDate) return "inherit";

    // Determine the delimiter used in the date string
    let delimiter = '/';
    if (simStatus.expiredDate.includes('-')) {
      delimiter = '-';
    }

    // Split the date string based on the delimiter
    const dateParts = simStatus.expiredDate.split(delimiter);

    // Log the split parts for debugging
    // console.log("Date Parts:", dateParts);

    // Ensure the date string has exactly 3 parts
    if (dateParts.length !== 3) {
      // console.error("Invalid date format:", simStatus.expiredDate);
      return "inherit";
    }

    // Parse the date components as integers
    let [part1, part2, part3] = dateParts.map(part => parseInt(part, 10));

    // **Assumption**: The format is 'DD-MM-YY'
    let day = part1;
    let month = part2;
    let year = part3;

    // Adjust the year to be in the 2000s if it's less than 100
    if (year < 100) {
      year += 2000;
    }

    // Validate the month
    if (month < 1 || month > 12) {
      // console.error("Invalid month value:", month);
      return "inherit";
    }

    // Validate the day
    if (day < 1 || day > 31) { // Further validation can be added based on the month
      // console.error("Invalid day value:", day);
      return "inherit";
    }

    // Construct the Date object (Note: Months are 0-indexed in JavaScript)
    const expiredDate = new Date(year, month - 1, day);



    // Check if the constructed date is valid
    if (isNaN(expiredDate.getTime())) {
      // console.error("Constructed date is invalid:", expiredDate);
      return "inherit";
    }

    const currentDate = new Date();

    // Calculate the difference in time (milliseconds)
    const diffTime = expiredDate - currentDate;

    // Convert the difference from milliseconds to days
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // console.log("Difference in Days:", diffDays);

    // Determine the color based on the difference in days
    if (diffDays < 0) {
      return "red"; // Expired
    } else if (diffDays <= 7) {
      return "yellow"; // Expiring soon
    } else {
      return "inherit"; // Not expiring soon
    }
  };

   // Step 2: Check pin code right when the component mounts or user changes
   useEffect(() => {
    // If user has no pin code, show the setup
    if (user && !user.pinCode) {
      setShowPincodeSetup(true);
    }
  }, [user]);

   // Step 3: onSuccess callback after the user sets a new pin
   const handleNewPinSuccess = (newPin) => {
    // We assume your backend call was successful
    // 1) Hide the dialog
    setShowPincodeSetup(false);

    // 2) Possibly refresh user or store the new pin in local context
    // For example, if your `user` is stored in a global store or via `initialize`
    // you can do something like:
    const updatedUser = { ...user, pinCode: newPin };
    initialize(updatedUser);

    // now user has a pin code
  };
  return (
    <Page title="car remote system">
      <Layout />
      <PinCodeSetupDialog
        open={showPincodeSetup}
        onClose={() => setShowPincodeSetup(false)}
        onSuccess={handleNewPinSuccess}
        phoneNumber={user?.phoneNumber} // pass the user’s phoneNumber
      />

      {/* 5) The confirm pin code dialog you already have */}
      <PinCodeConfirm
        open={!checkedPincode}
        onModalClose={() => {
          setCheckedPincode(true);
        }}
        onSuccess={confirmed}
      />
      <SeasonalAnimation />
      <Container maxWidth={"lg"} sx={{
        pt: { xs: 8, md: 11 },
        mt: { xs: 6, md: 8 },
        background: "transparent",
        backdropFilter: "blur(6px)",
        backgroundColor: "rgba(255,255,255,0.04)",
        overflow: "hidden",
      }}>
        {loading && (
          <FindingScreen
            message={message}
            description={description}
            buttonPosition={buttonPosition}
            showBirdAnimation={true}
          />
        )}
        <Grid
          container
          spacing={4}
          sx={{
            // backgroundImage: "url(images/back.svg)",
            background: "transparent",
            backdropFilter: "blur(6px)",
            backgroundColor: "rgba(255,255,255,0.04)",
            p: 3,
            borderRadius: 2,
            boxShadow: 3
          }}
        >
          <Grid
            item
            xs={12}
            md={6}
            textAlign={"center"}
            sx={{
              mt: iconChange ? "50px" : "",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              minHeight: { xs: 400, md: 500, lg: 600 },
              position: 'relative',
              backgroundColor: "rgba(255,255,255,0.05)",
              backdropFilter: "blur(10px)",
              borderRadius: 2,
              boxShadow: 4,
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                width: '100%',
                height: '100%'
              }}
            >
              {!iconChange &&
                (user?.device?.uix?.toLowerCase()?.includes("carv1") ||
                  user.device === null) && (
                  <Car
                    sx={{
                      cursor: "pointer",
                    }}
                    device={user.device}
                    color={color}
                    action={action}
                    status={deviceStatus}
                  />
                )}
              {!iconChange && user?.device?.uix?.includes("Car2") && (
                <CarSide
                  sx={{ cursor: "pointer" }}
                  device={user.device}
                  color={color}
                  action={action}
                  status={deviceStatus}
                />
              )}
              {iconChange && (
                <Box>
                  <ChipIcon
                    sx={{ width: { xs: "60%", sm: "70%" }, cursor: "pointer" }}
                    device={user.device}
                    color={getSimCardColor()}
                    action={action}
                    status={deviceStatus}
                  />
                </Box>
              )}
            </Box>
            {isMobile && (
              <Stack gap={2} position={"absolute"} top={"10%"} left={"3%"}>
                <Stack gap={2} direction={"row"} alignItems={"center"}>
                  {!checkOnline ? (
                    <Stack direction="row" alignItems="center" gap={1}>
                      <Iconify
                        icon={online ? "bx:signal-5" : "bx:no-signal"}
                        width={20}
                        height={20}
                        sx={{
                          color: deviceStatus?.rssi > 25 ? 'green' :
                                 deviceStatus?.rssi > 15 ? 'yellow' :
                                 deviceStatus?.rssi > 5 ? 'orange' : 'red'
                        }}
                      />
                      <Typography variant="subtitle2" className="digital-font signal-display">
                        {deviceStatus?.rssi ? `${deviceStatus.rssi} dBm` : "N/A"}
                      </Typography>
                    </Stack>
                  ) : (
                    <Iconify
                      icon={"eos-icons:three-dots-loading"}
                      width={20}
                      height={20}
                    />
                  )}
                </Stack>
                <Stack gap={2} direction={"row"} alignItems={"center"}>
                  <Iconify
                    icon={"carbon:temperature-max"}
                    width={20}
                    height={20}
                  />
                  <Typography variant="subtitle2" className="temperature-display automotive-font">
                    {deviceStatus?.temp
                      ? `${fShortenNumber(deviceStatus?.temp - 20)}°C`
                      : "N"}
                  </Typography>
                </Stack>
                <Stack gap={2} direction={"row"} alignItems={"center"}>
                  <Iconify icon={"carbon:humidity"} width={20} height={20} />
                  <Typography variant="subtitle2" className="automotive-font">
                    {deviceStatus?.hum || "N"}%
                  </Typography>
                </Stack>

                <Stack gap={2} direction={"row"} alignItems={"center"}>
                  <Iconify
                    icon={"la:car-battery"}
                    width={20}
                    height={20}
                    sx={{ color: deviceStatus?.volt > 11 ? "white" : "red" }}
                  />
                  <Typography
                    variant="subtitle2"
                    className={`voltage-display ${
                      deviceStatus?.volt > 13 ? 'high' :
                      deviceStatus?.volt > 11 ? 'medium' : 'low'
                    }`}
                  >
                    {`${deviceStatus?.volt || ""}V`}
                  </Typography>
                </Stack>

                <Stack gap={2} direction={"row"} alignItems={"center"}>
                  <Iconify
                    icon={"mdi:sim"}
                    width={20}
                    height={20}
                    onClick={checkSimcard}
                    sx={{ color: getSimCardColor() }}
                  />
                  <Stack>
                    <Typography variant="caption" className="technical-font" style={{ fontSize: '0.7rem' }}>
                      {simStatus.balance || "N/A"}
                    </Typography>
                    <Typography variant="caption" className="technical-font" style={{ fontSize: '0.7rem' }}>
                      {simStatus.expiredDate || "N/A"}
                    </Typography>
                  </Stack>
                </Stack>
              </Stack>
            )}
            {isMobile && (
              <Stack gap={2} position={"absolute"} top={"10%"} right={"3%"}>
                <Stack gap={2} direction={"row"} alignItems={"center"}>
                  <Iconify icon={"mdi:home-temperature-outline"} width={20} height={20} />
                  <WeatherDisplay simpleMode={true} />
                </Stack>
                <Stack gap={2} direction={"row"} alignItems={"center"}>
                  <Iconify icon={"bxs:hot"} width={20} height={20} />
                  <Typography variant="subtitle2" className="temperature-display automotive-font">{`${fShortenNumber(
                    deviceStatus?.temp || " "
                  )}°C`}</Typography>
                </Stack>
                <Stack gap={2} direction={"row"} justifyItems={"center"}>
                  <SatelliteAltIcon
                    sx={{ width: 20 }}
                    htmlColor={
                      deviceStatus?.Lat && deviceStatus?.Lat > 0
                        ? "info"
                        : deviceStatus?.Lat && parseInt(deviceStatus?.Lat) === 0
                          ? "red"
                          : "info"
                    }
                  />
                  <Typography variant="subtitle2" className="status-indicator">GPS</Typography>
                </Stack>
                <Stack gap={2} direction="row" alignItems="center">
  {/* Use any icon that conveys "license" or "remaining days" */}
  <Iconify
    icon="mdi:calendar-clock"
    width={20}
    height={20}
    sx={{ color: user?.status === 'expired' ? 'red' : (user?.remainDays <= 7 ? 'yellow' : 'inherit') }}
  />

  <Typography variant="subtitle2" className="digital-font-small">
    {user?.remainDays
      ? `${Math.floor(user.remainDays / (1000 * 60 * 60 * 24))}D`
      : 'N/A'
    }
  </Typography>
</Stack>
                <Stack gap={2} direction={'row'} justifyItems={'center'}>

                  <InfoOutlined sx={{ width: 20, }} />
                  <Typography className="technical-font">
    {deviceStatus?.ver !== undefined? `V${deviceStatus.ver}` : "V1.0.0"}
  </Typography>
                </Stack>

                {/* MQTT Connection Status for LwM2M protocol */}
                {protocol === 'tcp' && (
                  <Stack gap={2} direction={'row'} alignItems={'center'}>
                    <Iconify
                      icon={mqttConnecting ? "eos-icons:three-dots-loading" :
                            mqttConnected ? "mdi:wifi" : "mdi:wifi-off"}
                      width={20}
                      height={20}
                      sx={{
                        color: mqttConnecting ? 'orange' :
                               mqttConnected ? 'green' : 'red'
                      }}
                    />
                    <Typography
                      variant="subtitle2"
                      className={`protocol-display ${
                        mqttConnecting ? 'connecting' :
                        mqttConnected ? 'online' : 'offline'
                      }`}
                    >
                      {mqttConnecting ? 'Connecting...' :
                       mqttConnected ? 'LwM2M' : 'Offline'}
                    </Typography>
                  </Stack>
                )}
              </Stack>
            )}
          </Grid>

          <Grid item xs={12} md={6}>
            <Stack
              gap={{ xs: 2, md: 4 }}
              justifyContent="space-between"
              sx={{ height: "100%" }}
            >
              {!isMobile && (
                <>
                  <Typography variant="h4" className="car-status-display" sx={{ mt: 3 }}>
                    {t("home.informations")}
                  </Typography>
                  <Divider />
                  <Box
                    sx={{
                      display: "grid",
                      columnGap: 3,
                      rowGap: 3,
                      gridTemplateColumns: {
                        xs: "repeat(2, 1fr)",
                        sm: "repeat(3, 1fr)",
                        md: "repeat(3, 1fr)",
                        lg: "repeat(4, 1fr)"
                      },
                      '@media (min-width: 1200px)': {
                        gridTemplateColumns: "repeat(4, 1fr)",
                        columnGap: 4
                      }
                    }}
                  >
                    <Card
                      sx={{
                        minHeight: 120,
                        border: "1px solid",
                        borderColor: "grey.50048",
                        p: 3,
                        textAlign: "center",
                        transition: 'transform 0.2s',
                        '&:hover': {
                          transform: 'scale(1.05)',
                          boxShadow: 4
                        }
                      }}
                    >
                      {!checkOnline ? (
                        <Stack alignItems="center" gap={1}>
                          <Iconify
                            icon={online ? "bx:signal-5" : "bx:no-signal"}
                            width={30}
                            height={30}
                          />
                          <Typography variant="body2" className="digital-font signal-display">
                            {deviceStatus?.rssi ? `${deviceStatus.rssi} dBm` : "N/A"}
                          </Typography>
                        </Stack>
                      ) : (
                        <Iconify
                          icon={"eos-icons:three-dots-loading"}
                          width={30}
                          height={30}
                        />
                      )}
                    </Card>

                    <Card
                      sx={{
                        minHeight: 120,
                        border: "1px solid",
                        borderColor: "grey.50048",
                        p: 3,
                        textAlign: "center",
                        transition: 'transform 0.2s',
                        '&:hover': {
                          transform: 'scale(1.05)',
                          boxShadow: 4
                        }
                      }}
                    >
                      <Iconify
                        icon={"carbon:temperature-max"}
                        width={30}
                        height={30}
                      />
                      <Typography variant="body2" className="temperature-display automotive-font" sx={{ mt: 1 }}>
                        {deviceStatus?.temp
                          ? `${fShortenNumber(deviceStatus?.temp - 20)}°C`
                          : " "}
                      </Typography>
                    </Card>
                    <Card
                      sx={{
                        minHeight: 120,
                        border: "1px solid",
                        borderColor: "grey.50048",
                        p: 3,
                        textAlign: "center",
                        transition: 'transform 0.2s',
                        '&:hover': {
                          transform: 'scale(1.05)',
                          boxShadow: 4
                        }
                      }}
                    >
                      <Iconify
                        icon={"carbon:humidity"}
                        width={30}
                        height={30}
                      />
                      <Typography variant="body2" className="automotive-font" sx={{ mt: 1 }}>
                        {deviceStatus?.hum || "N"}%
                      </Typography>
                    </Card>
                    <Card
                      sx={{
                        minHeight: 120,
                        border: "1px solid",
                        borderColor: "grey.50048",
                        p: 3,
                        textAlign: "center",
                        transition: 'transform 0.2s',
                        '&:hover': {
                          transform: 'scale(1.05)',
                          boxShadow: 4
                        }
                      }}
                    >
                      <Iconify
                        icon={"la:car-battery"}
                        width={30}
                        height={30}
                        sx={{ color: deviceStatus?.volt > 11 ? "inherit" : "red" }}
                      />
                      <Typography
                        variant="body2"
                        className={`voltage-display ${
                          deviceStatus?.volt > 13 ? 'high' :
                          deviceStatus?.volt > 11 ? 'medium' : 'low'
                        }`}
                        sx={{ mt: 1 }}
                      >
                        {`${deviceStatus?.volt || "N/A"}V`}
                      </Typography>
                    </Card>
                    <Card
                      sx={{
                        minHeight: 120,
                        border: "1px solid",
                        borderColor: "grey.50048",
                        p: 3,
                        textAlign: "center",
                        transition: 'transform 0.2s',
                        '&:hover': {
                          transform: 'scale(1.05)',
                          boxShadow: 4
                        }
                      }}
                    >
                      <Iconify
                        icon={"mdi:sim"}
                        width={30}
                        height={30}
                        sx={{ color: getSimCardColor() }}
                      />
                      <Stack spacing={0.5} sx={{ mt: 1 }}>
                        <Typography variant="body2" className="technical-font">
                          Balance: {simStatus.balance || "N/A"}
                        </Typography>
                        <Typography variant="body2" className="technical-font">
                          Expires: {simStatus.expiredDate || "N/A"}
                        </Typography>
                      </Stack>
                    </Card>

                    {/* GPS Status Card */}
                    <Card
                      sx={{
                        minHeight: 120,
                        border: "1px solid",
                        borderColor: "grey.50048",
                        p: 3,
                        textAlign: "center",
                        transition: 'transform 0.2s',
                        '&:hover': {
                          transform: 'scale(1.05)',
                          boxShadow: 4
                        }
                      }}
                    >
                      <SatelliteAltIcon
                        sx={{
                          width: 30,
                          height: 30,
                          color: deviceStatus?.Lat && deviceStatus?.Lat > 0
                            ? "info"
                            : deviceStatus?.Lat && parseInt(deviceStatus?.Lat) === 0
                              ? "red"
                              : "info"
                        }}
                      />
                      <Typography variant="body2" className="status-indicator" sx={{ mt: 1 }}>
                        GPS Status
                      </Typography>
                    </Card>

                    {/* Remaining Days Card */}
                    <Card
                      sx={{
                        minHeight: 120,
                        border: "1px solid",
                        borderColor: "grey.50048",
                        p: 3,
                        textAlign: "center",
                        transition: 'transform 0.2s',
                        '&:hover': {
                          transform: 'scale(1.05)',
                          boxShadow: 4
                        }
                      }}
                    >
                      <Iconify
                        icon="mdi:calendar-clock"
                        width={30}
                        height={30}
                        sx={{
                          color: user?.status === 'expired'
                            ? 'red'
                            : (user?.remainDays <= 7 ? 'yellow' : 'inherit')
                        }}
                      />
                      <Typography variant="body2" className="digital-font-small" sx={{ mt: 1 }}>
                        {user?.remainDays
                          ? `${Math.floor(user.remainDays / (1000 * 60 * 60 * 24))}D`
                          : 'N/A'
                        }
                      </Typography>
                    </Card>

                    {/* Version Card */}
                    <Card
                      sx={{
                        minHeight: 120,
                        border: "1px solid",
                        borderColor: "grey.50048",
                        p: 3,
                        textAlign: "center",
                        transition: 'transform 0.2s',
                        '&:hover': {
                          transform: 'scale(1.05)',
                          boxShadow: 4
                        }
                      }}
                    >
                      <InfoOutlined sx={{ width: 30, height: 30 }} />
                      <Typography variant="body2" className="technical-font" sx={{ mt: 1 }}>
                        {deviceStatus?.ver !== undefined
                          ? `V${deviceStatus.ver}`
                          : "V1.0.0"}
                      </Typography>
                    </Card>

                    {/* MQTT Connection Status Card for LwM2M protocol */}
                    {protocol === 'tcp' && (
                      <Card
                        sx={{
                          minHeight: 120,
                          border: "1px solid",
                          borderColor: "grey.50048",
                          p: 3,
                          textAlign: "center",
                          transition: 'transform 0.2s',
                          '&:hover': {
                            transform: 'scale(1.05)',
                            boxShadow: 4
                          }
                        }}
                      >
                        <Iconify
                          icon={mqttConnecting ? "eos-icons:three-dots-loading" :
                                mqttConnected ? "mdi:wifi" : "mdi:wifi-off"}
                          width={30}
                          height={30}
                          sx={{
                            color: mqttConnecting ? 'orange' :
                                   mqttConnected ? 'green' : 'red'
                          }}
                        />
                        <Typography
                          variant="body2"
                          className={`protocol-display ${
                            mqttConnecting ? 'connecting' :
                            mqttConnected ? 'online' : 'offline'
                          }`}
                          sx={{ mt: 1 }}
                        >
                          {mqttConnecting ? 'Connecting...' :
                           mqttConnected ? 'LwM2M Connected' : 'LwM2M Offline'}
                        </Typography>
                      </Card>
                    )}

                  </Box>
                </>
              )}

              <Divider sx={{ mt: { xs: 2, md: iconChange ? "70px" : "30px" } }} />
              {user.status === "expired" && (
                <Alert
                  severity="warning"
                  sx={{ backgroundColor: "grey.50024" }}
                >
                  {t("words.license_has_expired")} <br />
                  {t("words.get_new_key")}
                </Alert>
              )}
              {iconChange && (
                <Box sx={{ flexGrow: 1 }}>
                  <DeviceController
                    flexGrow={1}
                    onTopLeft={handleOn1}
                    onTopRight={handleOn2}
                    onBottomLeft={handleOff1}
                    onBottomRight={handleOff2}
                    onCenter={handleLocation}
                    topLeftContent={
                      <Stack alignItems={"center"} justifyContent={"center"}>
                        <Iconify
                          icon={"fontisto:power"}
                          width={28}
                          height={28}
                        />
                        <Typography variant="caption">
                          {t("home.on1")}
                        </Typography>
                      </Stack>
                    }
                    topRightContent={
                      <Stack alignItems={"center"} justifyContent={"center"}>
                        <Iconify
                          icon={"fontisto:power"}
                          width={28}
                          height={28}
                        />
                        <Typography variant="caption">
                          {t("home.on2")}
                        </Typography>
                      </Stack>
                    }
                    bottomLeftContent={
                      <Stack alignItems={"center"} justifyContent={"center"}>
                        <Iconify
                          icon={"carbon:flash-off"}
                          width={28}
                          height={28}
                          sx={{ color: "red" }}
                        />
                        <Typography variant="caption" sx={{ color: "red" }}>
                          {t("home.off1")}
                        </Typography>
                      </Stack>
                    }
                    bottomRightContent={
                      <ScrollablePowerButton
                        onPowerClick={handlePowerClick}
                        onScheduleClick={handleScheduleClick}
                        powerLabel={t("home.off2")}
                        scheduleLabel={t("words.schedule", "Schedule")}
                        powerIcon="carbon:flash-off"
                        scheduleIcon="material-symbols:alarm"
                        powerColor="red"
                        scheduleColor="warning.main"
                        disabled={loading}
                        showScheduleIndicator={true}
                      />
                    }
                    centerContent={
                      <Iconify
                        icon={`fluent:location${viewMap ? "-off" : ""
                          }-20-filled`}
                        width={40}
                        height={40}
                      />
                    }
                  />
                </Box>
              )}
              {!iconChange && (
                <Box sx={{ flexGrow: 1, display: 'flex', justifyContent: 'center', mt: 4 }}>
                  <Stack
                    direction="row"
                    spacing={{ xs: 2, sm: 4 }}
                    alignItems="center"
                  >
                    {/* Lock/Unlock Button */}
                    <motion.div
                      animate={bouncingButton === 'lock' ? {
                        y: [0, -8, 0, -6, 0, -4, 0],
                        scale: [1, 1.05, 1, 1.03, 1, 1.02, 1]
                      } : {}}
                      transition={{
                        duration: 0.6,
                        repeat: bouncingButton === 'lock' ? Infinity : 0,
                        ease: "easeInOut"
                      }}
                    >
                      <Box
                        onClick={handleLockToggle}
                        onContextMenu={(e) => e.preventDefault()}
                        sx={{
                        width: 90,
                        height: 90,
                        borderRadius: '50%',
                        background: "radial-gradient(circle at center, #333 0%, #1a1a1a 70%)",
                        border: "2px solid rgba(255,255,255,0.25)",
                        boxShadow: "0 8px 32px rgba(0,0,0,0.37)",
                        transition: "all 0.25s ease-in-out",
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        cursor: 'pointer',
                        position: 'relative',
                        // Ensure button is properly clickable
                        userSelect: 'none',
                        WebkitUserSelect: 'none',
                        MozUserSelect: 'none',
                        msUserSelect: 'none',
                        WebkitTouchCallout: 'none',
                        WebkitTapHighlightColor: 'transparent',
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          top: -5,
                          left: -5,
                          right: -5,
                          bottom: -5,
                          borderRadius: '50%',
                          border: '2px solid rgba(255,255,255,0.15)',
                          boxShadow: '0 0 10px rgba(255,255,255,0.25)',
                        },
                        '&:hover': {
                          transform: "scale(1.05)",
                          boxShadow: "0 0 14px rgba(255,255,255,0.3), 0 0 20px rgba(255,255,255,0.1)",
                        },
                        '&:active': {
                          transform: 'scale(0.95)',
                          boxShadow: `
                            inset 0 4px 8px rgba(0,0,0,0.4),
                            inset 0 -2px 4px rgba(255,255,255,0.2),
                            0 2px 8px rgba(0,0,0,0.3)
                          `,
                        },
                      }}
                    >
                      <Iconify
                        icon={isLocked ? "material-symbols:lock-open" : "material-symbols:lock"}
                        width={36}
                        height={36}
                        sx={{
                          color: 'white',
                          mb: 0.5,
                          // Prevent icon from intercepting clicks
                          pointerEvents: 'none',
                        }}
                      />
                      <Typography
                        variant="caption"
                        sx={{
                          color: 'white',
                          fontSize: '0.7rem',
                          // Prevent text selection and click interception
                          userSelect: 'none',
                          WebkitUserSelect: 'none',
                          MozUserSelect: 'none',
                          msUserSelect: 'none',
                          pointerEvents: 'none',
                        }}
                      >
                        {isLocked ? t("home.unlock") : t("home.lock")}
                      </Typography>
                    </Box>
                    </motion.div>

                    {/* Modern Car Power Button */}
                    <motion.div
                      animate={bouncingButton === 'power' ? {
                        y: [0, -10, 0, -8, 0, -6, 0],
                        scale: [1, 1.08, 1, 1.05, 1, 1.03, 1],
                        rotate: [0, 2, 0, -2, 0, 1, 0]
                      } : {}}
                      transition={{
                        duration: 0.8,
                        repeat: bouncingButton === 'power' ? Infinity : 0,
                        ease: "easeInOut"
                      }}
                    >
                      <Box
                        onMouseDown={handleEngineMouseDown}
                        onMouseUp={handleEngineMouseUp}
                        onMouseLeave={handleEngineMouseUp}
                        onTouchStart={handleEngineMouseDown}
                        onTouchEnd={handleEngineMouseUp}
                        onTouchCancel={handleEngineMouseUp}
                        onContextMenu={(e) => e.preventDefault()}
                        sx={{
                        width: 100,
                        height: 100,
                        borderRadius: '50%',
                        position: 'relative',
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        // Ensure button is properly clickable
                        userSelect: 'none',
                        WebkitUserSelect: 'none',
                        MozUserSelect: 'none',
                        msUserSelect: 'none',
                        WebkitTouchCallout: 'none',
                        WebkitTapHighlightColor: 'transparent',
                        // Outer ring - brushed metal effect
                        background: `
                          radial-gradient(circle at 30% 30%, rgba(255,255,255,0.8) 0%, rgba(200,200,200,0.4) 30%, rgba(100,100,100,0.8) 100%),
                          conic-gradient(from 0deg, #2a2a2a, #4a4a4a, #2a2a2a, #4a4a4a, #2a2a2a)
                        `,
                        border: '3px solid #1a1a1a',
                        boxShadow: `
                          inset 0 2px 4px rgba(255,255,255,0.3),
                          inset 0 -2px 4px rgba(0,0,0,0.3),
                          0 4px 12px rgba(0,0,0,0.4),
                          0 0 0 1px rgba(255,255,255,0.1)
                        `,
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          top: '50%',
                          left: '50%',
                          transform: 'translate(-50%, -50%)',
                          width: '85%',
                          height: '85%',
                          borderRadius: '50%',
                          background: isEngineOn
                            ? `radial-gradient(circle at center, rgba(0,255,100,0.3) 0%, rgba(0,200,80,0.1) 70%, transparent 100%)`
                            : `radial-gradient(circle at center, rgba(100,100,100,0.2) 0%, rgba(50,50,50,0.1) 70%, transparent 100%)`,
                          border: isEngineOn
                            ? '2px solid rgba(0,255,100,0.6)'
                            : '2px solid rgba(255,255,255,0.2)',
                          boxShadow: isEngineOn
                            ? `
                              0 0 20px rgba(0,255,100,0.4),
                              inset 0 0 20px rgba(0,255,100,0.2),
                              0 0 40px rgba(0,255,100,0.2)
                            `
                            : 'inset 0 1px 2px rgba(255,255,255,0.2), inset 0 -1px 2px rgba(0,0,0,0.2)',
                          animation: isEngineOn ? 'powerPulse 2s ease-in-out infinite' : 'none',
                          zIndex: 1,
                        },
                        '&::after': {
                          content: '""',
                          position: 'absolute',
                          top: '50%',
                          left: '50%',
                          transform: 'translate(-50%, -50%)',
                          width: '70%',
                          height: '70%',
                          borderRadius: '50%',
                          background: `
                            radial-gradient(circle at 30% 30%, rgba(80,80,80,1) 0%, rgba(40,40,40,1) 50%, rgba(20,20,20,1) 100%)
                          `,
                          border: '1px solid rgba(0,0,0,0.5)',
                          boxShadow: `
                            inset 0 2px 4px rgba(0,0,0,0.3),
                            inset 0 -1px 2px rgba(255,255,255,0.1)
                          `,
                          zIndex: 2,
                        },
                        '&:hover': {
                          transform: 'scale(1.05)',
                          boxShadow: `
                            inset 0 2px 4px rgba(255,255,255,0.4),
                            inset 0 -2px 4px rgba(0,0,0,0.4),
                            0 6px 16px rgba(0,0,0,0.5),
                            0 0 0 1px rgba(255,255,255,0.2),
                            0 0 20px rgba(255,255,255,0.1)
                          `,
                        },
                        '&:active': {
                          transform: 'scale(0.95)',
                          boxShadow: `
                            inset 0 4px 8px rgba(0,0,0,0.4),
                            inset 0 -2px 4px rgba(255,255,255,0.2),
                            0 2px 8px rgba(0,0,0,0.3)
                          `,
                        },
                        // Add keyframe animation for pulsing effect
                        '@keyframes powerPulse': {
                          '0%, 100%': {
                            boxShadow: `
                              0 0 20px rgba(0,255,100,0.4),
                              inset 0 0 20px rgba(0,255,100,0.2),
                              0 0 40px rgba(0,255,100,0.2)
                            `,
                            borderColor: 'rgba(0,255,100,0.6)',
                          },
                          '50%': {
                            boxShadow: `
                              0 0 30px rgba(0,255,100,0.6),
                              inset 0 0 30px rgba(0,255,100,0.3),
                              0 0 60px rgba(0,255,100,0.3)
                            `,
                            borderColor: 'rgba(0,255,100,0.8)',
                          },
                        },
                      }}
                    >
                      {/* Progress fill animation for hold gesture */}
                      <Box
                        sx={{
                          position: 'absolute',
                          top: '50%',
                          left: '50%',
                          transform: 'translate(-50%, -50%)',
                          width: '85%',
                          height: '85%',
                          borderRadius: '50%',
                          background: `conic-gradient(from 0deg, rgba(0,255,100,0.8) 0%, rgba(0,255,100,0.8) ${engineHoldProgress}%, transparent ${engineHoldProgress}%, transparent 100%)`,
                          opacity: isEngineHolding ? 1 : 0,
                          transition: 'opacity 0.2s',
                          zIndex: 3,
                        }}
                      />

                      {/* Power symbol */}
                      <Box
                        sx={{
                          position: 'relative',
                          zIndex: 4,
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          justifyContent: 'center',
                          // Prevent text from intercepting clicks
                          pointerEvents: 'none',
                        }}
                      >
                        <Box
                          sx={{
                            fontSize: '32px',
                            color: isEngineOn ? '#00ff64' : '#ffffff',
                            filter: isEngineOn ? 'drop-shadow(0 0 8px rgba(0,255,100,0.8))' : 'none',
                            transition: 'all 0.3s ease',
                            mb: 0.5,
                            fontWeight: 'bold',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            // Prevent text selection and click interception
                            userSelect: 'none',
                            WebkitUserSelect: 'none',
                            MozUserSelect: 'none',
                            msUserSelect: 'none',
                            pointerEvents: 'none',
                          }}
                        >
                          ⏻
                        </Box>
                        <Typography
                          variant="caption"
                          sx={{
                            color: isEngineOn ? '#00ff64' : '#ffffff',
                            fontSize: '0.65rem',
                            fontWeight: 600,
                            textShadow: isEngineOn ? '0 0 8px rgba(0,255,100,0.6)' : 'none',
                            transition: 'all 0.3s ease',
                            textAlign: 'center',
                            lineHeight: 1,
                            // Prevent text selection and click interception
                            userSelect: 'none',
                            WebkitUserSelect: 'none',
                            MozUserSelect: 'none',
                            msUserSelect: 'none',
                            pointerEvents: 'none',
                          }}
                        >
                          {isEngineHolding
                            ? `${Math.ceil((3000 - (engineHoldProgress * 30)) / 1000)}s`
                            : (isEngineOn ? 'ENGINE' : 'START')
                          }
                        </Typography>
                      </Box>
                    </Box>
                    </motion.div>

                    {/* Location Show/Hide Button */}
                    <motion.div
                      animate={bouncingButton === 'location' ? {
                        y: [0, -8, 0, -6, 0, -4, 0],
                        scale: [1, 1.05, 1, 1.03, 1, 1.02, 1],
                        rotate: [0, 1, 0, -1, 0]
                      } : {}}
                      transition={{
                        duration: 0.6,
                        repeat: bouncingButton === 'location' ? Infinity : 0,
                        ease: "easeInOut"
                      }}
                    >
                      <Box
                        onClick={handleLocationToggle}
                        onContextMenu={(e) => e.preventDefault()}
                        sx={{
                        width: 90,
                        height: 90,
                        borderRadius: '50%',
                        background: "radial-gradient(circle at center, #333 0%, #1a1a1a 70%)",
                        border: "2px solid rgba(255,255,255,0.25)",
                        boxShadow: "0 8px 32px rgba(0,0,0,0.37)",
                        transition: "all 0.25s ease-in-out",
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        cursor: 'pointer',
                        position: 'relative',
                        // Ensure button is properly clickable
                        userSelect: 'none',
                        WebkitUserSelect: 'none',
                        MozUserSelect: 'none',
                        msUserSelect: 'none',
                        WebkitTouchCallout: 'none',
                        WebkitTapHighlightColor: 'transparent',
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          top: -5,
                          left: -5,
                          right: -5,
                          bottom: -5,
                          borderRadius: '50%',
                          border: '2px solid rgba(255,255,255,0.15)',
                          boxShadow: '0 0 10px rgba(255,255,255,0.25)',
                        },
                        '&:hover': {
                          transform: "scale(1.05)",
                          boxShadow: "0 0 14px rgba(255,255,255,0.3), 0 0 20px rgba(255,255,255,0.1)",
                        },
                        '&:active': {
                          transform: 'scale(0.95)',
                          boxShadow: `
                            inset 0 4px 8px rgba(0,0,0,0.4),
                            inset 0 -2px 4px rgba(255,255,255,0.2),
                            0 2px 8px rgba(0,0,0,0.3)
                          `,
                        },
                      }}
                    >
                      <Iconify
                        icon={viewMap ? "material-symbols:location-off" : "material-symbols:location-on"}
                        width={36}
                        height={36}
                        sx={{
                          color: 'white',
                          mb: 0.5,
                          // Prevent icon from intercepting clicks
                          pointerEvents: 'none',
                        }}
                      />
                      <Typography
                        variant="caption"
                        sx={{
                          color: 'white',
                          fontSize: '0.6rem',
                          textAlign: 'center',
                          lineHeight: 1,
                          maxWidth: '70px',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          // Prevent text selection and click interception
                          userSelect: 'none',
                          WebkitUserSelect: 'none',
                          MozUserSelect: 'none',
                          msUserSelect: 'none',
                          pointerEvents: 'none',
                        }}
                      >
                        {viewMap ? "Hide" : "Show"}
                      </Typography>
                    </Box>
                    </motion.div>
                  </Stack>
                </Box>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12}>
            {viewMap &&
              deviceStatus?.Lat &&
              deviceStatus?.Lon &&
              deviceStatus?.Lat !== 0 &&
              deviceStatus?.Lon !==0 && (
                <CarLocation lat={deviceStatus?.Lat} lng={deviceStatus?.Lon} />
              )}
          </Grid>
        </Grid>
      </Container>

      <PinCodeConfirm
        open={!checkedPincode}
        onModalClose={() => {
          setCheckedPincode(true);
        }}
        onSuccess={confirmed}
      />

      {/* Countdown Timer - REMOVED for better bird animation */}
      {/* {isTimerVisible && (
        <Typography variant="h4" align="center" sx={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', color: 'green', fontFamily: 'digit-7', fontWeight: 'bold', fontSize: '5rem' }}>
          {countdown}
        </Typography>
      )} */}

      {/* Scheduler Dialog */}
      <SchedulerDialog
        open={schedulerDialogOpen}
        onClose={handleSchedulerClose}
        deviceNumber={user?.device?.deviceNumber}
        deviceType={user?.device?.type}
      />
    </Page>
  );
}
